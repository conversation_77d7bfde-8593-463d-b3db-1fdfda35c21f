import React, { useState, useCallback, useEffect } from 'react';

// We'll load the xlsx library from a CDN.
// In a real project, you would install it via npm.
const XLSX_CDN = "https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js";


const institutionTypes = {
  320032: "สถาบันเดียวกัน",
  320033: "ต่างสถาบัน"
};

const costThresholds = {
  "0753500001": "<=500",
  "0753500002": "<=1000",
  "0753500003": "<=2000",
  "0753500004": "<=5000",
  "0753500005": "<=10000",
  "0753500006": "<=30000",
  "0753500007": ">30000"
};


// Default values for criteria
const DEFAULT_CRITERIA1 = [320032, 320033];
const DEFAULT_CRITERIA2 = [
    "0753500001", "0753500002", "0753500003", "0753500004","0753500005", "0753500006", "0753500007"
];
function App() {
    const [fileName, setFileName] = useState(null);
    const [results, setResults] = useState([]); // Master list of all results
    const [filteredResults, setFilteredResults] = useState([]); // Results to display after filtering
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(false);
    const [uniqueValues, setUniqueValues] = useState({ criteria1: [], criteria2: [] });
    const [filters, setFilters] = useState({ criteria1: 'all', criteria2: 'all' });

    // Effect to apply filters whenever the master results or the filters change
    useEffect(() => {
        let data = [...results];

        if (filters.criteria1 !== 'all') {
            data = data.filter(row => String(row.criteria1) === filters.criteria1);
        }
        if (filters.criteria2 !== 'all') {
            data = data.filter(row => String(row.criteria2) === filters.criteria2);
        }
        setFilteredResults(data);
    }, [results, filters]);


    // Function to ensure the xlsx library is loaded
    const loadXlsx = useCallback(() => {
        return new Promise((resolve, reject) => {
            if (window.XLSX) {
                resolve(window.XLSX);
                return;
            }
            const script = document.createElement('script');
            script.src = XLSX_CDN;
            script.onload = () => {
                if (window.XLSX) {
                    resolve(window.XLSX);
                } else {
                    reject(new Error("Failed to load xlsx library."));
                }
            };
            script.onerror = () => reject(new Error("Error loading xlsx library from CDN."));
            document.head.appendChild(script);
        });
    }, []);

    const handleFileChange = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        setFileName(file.name);
        setLoading(true);
        setError(null);
        setResults([]);
        setFilters({ criteria1: 'all', criteria2: 'all' }); // Reset filters on new file upload

        try {
            const XLSX = await loadXlsx();
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    // --- Step 1: Get all required sheets ---
                    const txnBblSheet = workbook.Sheets['TXN BBL'];
                    const txnBaySheet = workbook.Sheets['TXN BAY'];

                    if (!txnBblSheet) throw new Error("Sheet 'TXN BBL' not found.");
                    if (!txnBaySheet) throw new Error("Sheet 'TXN BAY' not found.");

                    const txnBblData = XLSX.utils.sheet_to_json(txnBblSheet, { header: 1 });
                    const txnBayData = XLSX.utils.sheet_to_json(txnBaySheet, { header: 1 });

                    const getCellValue = (sheet, cellAddress) => {
                        const cell = sheet[cellAddress];
                        return (cell ? cell.v : undefined);
                    };
                    
                    const calculatedResults = [];
                    for (let k = 0; k < DEFAULT_CRITERIA1.length; k++) {
                        for (let i = 0; i < DEFAULT_CRITERIA2.length; i++) {
                            const criteria1 = DEFAULT_CRITERIA1[k];
                            console.log("criteria1",criteria1)
                            const criteria2 = DEFAULT_CRITERIA2[i];
                            console.log("criteria2",criteria2)
                            if (criteria1 === undefined || criteria1 === null) {
                                break;
                            }

                            let totalRecords = 0;
                            let totalValue = 0;

                            for (let j = 1; j < txnBblData.length; j++) {
                                const row = txnBblData[j];
                                console.log("row",row)
                                if (row[10] === criteria1 && row[11] === criteria2) {
                                    totalRecords++;
                                    totalValue += Number(row[9]) || 0;
                                }
                            }

                            for (let j = 1; j < txnBayData.length; j++) {
                                const row = txnBayData[j];
                                if (row[16] === criteria1 && row[17] === criteria2) {
                                    totalRecords++;
                                    totalValue += Number(row[10]) || 0;
                                }
                            }

                            calculatedResults.push({
                                criteria1,
                                criteria2,
                                totalRecords,
                                totalValue,
                            });
                        }
                    }

                    if (calculatedResults.length === 0) {
                        throw new Error("No valid criteria found in 'Summary' sheet rows 10-16.");
                    }
                    
                    // --- Populate master results and extract unique values for filters ---
                    setResults(calculatedResults);
                    const uniqueCrit1 = [...new Set(calculatedResults.map(r => r.criteria1))];
                    const uniqueCrit2 = [...new Set(calculatedResults.map(r => r.criteria2))];
                    setUniqueValues({ criteria1: uniqueCrit1, criteria2: uniqueCrit2 });


                } catch (err) {
                    setError(err.message);
                    console.error(err);
                } finally {
                    setLoading(false);
                }
            };
            
            reader.onerror = (err) => {
                 setError("Failed to read the file.");
                 setLoading(false);
            }

            reader.readAsArrayBuffer(file);

        } catch (err) {
            setError(err.message);
            setLoading(false);
        }
    };

    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters(prev => ({ ...prev, [name]: value }));
    };

    return (
        <div className="bg-slate-50 min-h-screen flex flex-col items-center font-sans p-4 sm:p-6 lg:p-8">
            <div className="w-full max-w-5xl mx-auto bg-white rounded-xl shadow-lg p-8">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-slate-800">Excel Summary Calculator</h1>
                    <p className="text-slate-500 mt-2">Upload a file to calculate and filter Total Records and Total Values.</p>
                </div>

                {/* File Upload Area */}
                <div className="mb-6 max-w-2xl mx-auto">
                    <label htmlFor="file-upload" className="cursor-pointer w-full inline-flex items-center justify-center rounded-lg border-2 border-dashed border-slate-300 bg-slate-50 hover:bg-slate-100 p-8 text-center transition">
                        <div className="text-slate-600">
                            <svg className="w-12 h-12 mx-auto mb-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg>
                            <h3 className="font-medium text-slate-700">
                                {fileName ? `Selected: ${fileName}` : "Click to upload a file"}
                            </h3>
                            <p className="text-sm text-slate-500 mt-1">XLSX or XLS files</p>
                        </div>
                    </label>
                    <input id="file-upload" type="file" className="hidden" accept=".xlsx, .xls" onChange={handleFileChange} />
                </div>

                {/* Loading Indicator */}
                {loading && (
                    <div className="flex items-center justify-center space-x-2 p-4">
                        <div className="w-4 h-4 rounded-full animate-pulse bg-blue-600"></div>
                        <div className="w-4 h-4 rounded-full animate-pulse bg-blue-600" style={{animationDelay: '0.2s'}}></div>
                        <div className="w-4 h-4 rounded-full animate-pulse bg-blue-600" style={{animationDelay: '0.4s'}}></div>
                        <span className="text-slate-600">Calculating...</span>
                    </div>
                )}

                {/* Error Message */}
                {error && (
                    <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert">
                        <p className="font-bold">Error</p>
                        <p>{error}</p>
                    </div>
                )}

                {/* Filters and Results */}
                {results.length > 0 && (
                    <div className="mt-8">
                        {/* Filter Controls */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 p-4 bg-slate-50 rounded-lg border">
                            <div>
                                <label htmlFor="filter-criteria2" className="block text-sm font-medium text-slate-700 mb-1">ช่วงมูลค่า (Value Range)</label>
                                <select id="filter-criteria2" name="criteria2" value={filters.criteria2} onChange={handleFilterChange} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                    <option value="all">All</option>
                                    {uniqueValues.criteria2.map(val => <option key={val} value={val}>{val}</option>)}
                                </select>
                            </div>
                            <div>
                                <label htmlFor="filter-criteria1" className="block text-sm font-medium text-slate-700 mb-1">ประเภทการทำธุรกรรม (Transaction Type)</label>
                                <select id="filter-criteria1" name="criteria1" value={filters.criteria1} onChange={handleFilterChange} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                    <option value="all">All</option>
                                    {uniqueValues.criteria1.map(val => <option key={val} value={val}>{val}</option>)}
                                </select>
                            </div>
                        </div>

                        {/* Results Table */}
                        <h2 className="text-2xl font-semibold text-slate-800 mb-4 text-center">Calculation Results</h2>
                        <div className="overflow-x-auto rounded-lg border border-slate-200">
                            <table className="min-w-full divide-y divide-slate-200">
                                <thead className="bg-slate-50">
                                    <tr>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">ช่วงมูลค่า</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">ประเภทการทำธุรกรรม</th>
                                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Total Records</th>
                                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Total Value</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-slate-200">
                                    {filteredResults.map((row, index) => (
                                        <tr key={index} className="hover:bg-slate-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-900">{row.criteria2}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{row.criteria1}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600 text-right font-mono">{row.totalRecords.toLocaleString()}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600 text-right font-mono">{row.totalValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
                                        </tr>
                                    ))}
                                </tbody>
                                
                            </table>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default App;
