import React, { useState, useEffect } from "react";
import { saveAs } from "file-saver";
import CustomSelect from "./components/CustomSelect";

// --- Helper Functions ---

// Function to convert ArrayBuffer to a format that xlsx library can read
const s2ab = (s) => {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; i++) {
    view[i] = s.charCodeAt(i) & 0xff;
  }
  return buf;
};

// Loading Spinner Component
const LoadingSpinner = ({ size = "w-6 h-6", color = "text-indigo-600" }) => (
  <svg
    className={`animate-spin ${size} ${color}`}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
);

// --- Main App Component ---

export default function App() {
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [fileName, setFileName] = useState("");
  const [xlsxLoaded, setXlsxLoaded] = useState(false);

  // State for holding table data
  const [tableHeaders, setTableHeaders] = useState([]);
  const [tableRows, setTableRows] = useState([]);

  // State for multiple file upload
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [dragActive, setDragActive] = useState(false);


  // State for bank type selection
  const [selectedBankType, setSelectedBankType] = useState("BBL");

  // State for transaction type filter
  const [selectedTransactionType, setSelectedTransactionType] = useState("all");

  // State for loading states
  const [summaryLoading, setSummaryLoading] = useState(false);

  // State for summary report
  const [summaryData, setSummaryData] = useState([]);
  const [summaryGenerated, setSummaryGenerated] = useState(false);

  // Effect hook to load the XLSX script from a CDN
  useEffect(() => {
    if (window.XLSX) {
      setXlsxLoaded(true);
      return;
    }

    const XLSX_CDN =
      "https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js";
    const script = document.createElement("script");
    script.src = XLSX_CDN;
    script.async = true;

    script.onload = () => setXlsxLoaded(true);
    script.onerror = () =>
      setError(
        "Could not load the Excel library. Please check your internet connection and refresh."
      );

    document.body.appendChild(script);

    return () => {
      const existingScript = document.querySelector(
        `script[src="${XLSX_CDN}"]`
      );
      if (existingScript) document.body.removeChild(existingScript);
    };
  }, []);

  // Cost thresholds map
  const costThresholds = {
    "0753500001": "<=500",
    "0753500002": "<=1000",
    "**********": "<=2000",
    "**********": "<=5000",
    "**********": "<=10000",
    "**********": "<=30000",
    "**********": ">30000",
  };

  // Function to generate summary report
  const generateSummaryReport = () => {
    const summary = {};
    
    tableRows.forEach((row) => {
      const transactionType = row[row.length - 3]; // ประเภทการทำธุรกรรม
      const valueRange = row[row.length - 2]; // ช่วงมูลค่า
      const bankType = row[row.length - 1]; // ประเภทธนาคาร
      const amount = parseFloat(row[bankType === "BBL" ? 8 : 10]) || 0; // Amount from column I (BBL) or K (BAY)

      // Convert transaction type to string for consistent comparison
      const transactionTypeStr = String(transactionType).trim();
      const selectedTransactionTypeStr = String(selectedTransactionType).trim();

      // Apply transaction type filter
      if (
        selectedTransactionType !== "all" &&
        transactionTypeStr !== selectedTransactionTypeStr
      ) {
        return;
      }

      const key = `${transactionType}_${valueRange}`;

      if (!summary[key]) {
        summary[key] = {
          transactionType: transactionTypeStr,
          valueRange,
          count: 0,
          totalAmount: 0,
        };
      }

      summary[key].count += 1;
      summary[key].totalAmount += amount;
    });

    return Object.values(summary).sort((a, b) => {
      if (a.transactionType !== b.transactionType) {
        return String(a.transactionType).localeCompare(
          String(b.transactionType)
        );
      }
      return String(a.valueRange).localeCompare(String(b.valueRange));
    });
  };

  // Bank type options
  const bankTypes = [
    { value: "BBL", label: "BBL - Bangkok Bank" },
    { value: "BAY", label: "BAY - Bank of Ayudhya" },
  ];

  // Transaction type options
  const transactionTypes = [
    { value: "all", label: "ทั้งหมด (All)" },
    { value: "320032", label: "320032" },
    { value: "320033", label: "320033" },
  ];

  // Handler for bank type selection
  const handleBankTypeChange = (e) => {
    setSelectedBankType(e.target.value);
  };

  // Handler for transaction type filter
  const handleTransactionTypeChange = (e) => {
    setSelectedTransactionType(e.target.value);
    // Auto-regenerate summary when filter changes if summary was already generated
    if (summaryGenerated && tableRows.length > 0) {
      setSummaryLoading(true);
      setTimeout(() => {
        const summary = generateSummaryReport();
        setSummaryData(summary);
        setSummaryLoading(false);
      }, 500);
    }
  };

  // Handler for generating summary report
  const handleGenerateSummary = () => {
    setSummaryLoading(true);
    
    // Simulate processing time for better UX
    setTimeout(() => {
      const summary = generateSummaryReport();
      setSummaryData(summary);
      setSummaryGenerated(true);
      setSummaryLoading(false);
    }, 1000);
  };

  /**
   * Determines the value for the 'ช่วงมูลค่า' column based on the value from column K.
   */
  const getValueRangeCode = (kValue) => {
    const value = parseFloat(kValue);
    if (isNaN(value)) return "N/A";

    if (value <= 500) return "0753500001";
    if (value <= 1000) return "0753500002";
    if (value <= 2000) return "**********";
    if (value <= 5000) return "**********";
    if (value <= 10000) return "**********";
    if (value <= 30000) return "**********";
    if (value > 30000) return "**********";

    return "Other";
  };

  /**
   * Validates if a file has an acceptable format
   */
  const validateFileFormat = (file) => {
    const validExtensions = [".xlsx", ".xls", ".csv"];
    const fileExtension = "." + file.name.split(".").pop().toLowerCase();
    return validExtensions.includes(fileExtension);
  };

  /**
   * Processes a single file and returns the processed data
   */
  const processFile = async (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const data = event.target.result;
          const workbook = window.XLSX.read(data, { type: "binary" });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const json_data = window.XLSX.utils
            .sheet_to_json(worksheet, {
              header: 1,
            })
            .filter((row) => {
              return row.some(
                (cell) =>
                  cell !== undefined &&
                  cell !== null &&
                  cell.toString().trim() !== ""
              );
            });

          if (json_data.length === 0) {
            reject(new Error("The Excel file is empty or couldn't be read."));
            return;
          }

          // Detect bank type from filename
          const isBBL = file.name.toLowerCase().includes('bbl');
          
          let colK_Index = 10; // AMOUNT column for BAY
          let colP_Index = 15; // BY column for BAY
          if (isBBL) {
            colP_Index = 7; // Bank Code
            colK_Index = 8; // Amount
          }

          const processedData = json_data.map((row, index) => {
            if (index === 0) {
              return [...row, "ประเภทการทำธุรกรรม", "ช่วงมูลค่า", "ประเภทธนาคาร"];
            }
            const pValue = row[colP_Index];
            const kValue = row[colK_Index];

            let transactionType;
            if (isBBL) {
              transactionType = !isNaN(pValue.trim()) && !isNaN(parseFloat(pValue.trim())) ? 320032 : 320033;
            } else {
              transactionType = String(pValue).trim() === "025" ? 320032 : 320033;
            }

            const valueRangeCode = getValueRangeCode(kValue);
            const bankType = isBBL ? "BBL" : "BAY";
            return [...row, transactionType, valueRangeCode, bankType];
          });

          resolve({
            fileName: file.name,
            headers: processedData[0] || [],
            rows: processedData.slice(1),
            processedData: processedData
          });
        } catch (err) {
          reject(err);
        }
      };
      reader.onerror = () => reject(new Error("Failed to read the file."));
      reader.readAsBinaryString(file);
    });
  };

  /**
   * Handles multiple file processing
   */
  const processMultipleFiles = async (files, append = false) => {
    setProcessing(true);
    setError(null);
    
    const validFiles = [];
    const invalidFiles = [];

    // Validate files
    files.forEach(file => {
      if (validateFileFormat(file)) {
        // Check if file already exists when appending
        if (append && uploadedFiles.some(existingFile => existingFile.name === file.name)) {
          invalidFiles.push(`${file.name} (ไฟล์ซ้ำ)`);
        } else {
          validFiles.push(file);
        }
      } else {
        invalidFiles.push(file.name);
      }
    });

    if (invalidFiles.length > 0) {
      setError(`ไฟล์รูปแบบไม่ถูกต้องหรือซ้ำ: ${invalidFiles.join(', ')}. กรุณาอัปโหลดไฟล์ในรูปแบบ .xlsx, .xls, .csv เท่านั้น`);
    }

    if (validFiles.length === 0) {
      setProcessing(false);
      return;
    }

    try {
      const processedResults = await Promise.all(
        validFiles.map(file => processFile(file))
      );

      // Combine all processed data
      let combinedHeaders = append && tableHeaders.length > 0 ? tableHeaders : [];
      let combinedRows = append ? [...tableRows] : [];
      const filesList = append ? [...uploadedFiles] : [];

      processedResults.forEach((result, index) => {
        if ((index === 0 && !append) || (append && combinedHeaders.length === 0)) {
          combinedHeaders = result.headers;
        }
        combinedRows = [...combinedRows, ...result.rows];
        filesList.push({
          name: result.fileName,
          rowCount: result.rows.length
        });
      });

      setUploadedFiles(filesList);
      setTableHeaders(combinedHeaders);
      setTableRows(combinedRows);
      
      const totalFiles = filesList.length;
      const actionText = append ? "เพิ่มแล้ว" : "ประมวลผลแล้ว";
      setFileName(`${totalFiles} ไฟล์${actionText}`);
      
      // Reset summary when new files are added
      setSummaryData([]);
      setSummaryGenerated(false);
      
    } catch (err) {
      console.error("Error processing files:", err);
      setError(`เกิดข้อผิดพลาด: ${err.message}`);
    } finally {
      setProcessing(false);
    }
  };

  /**
   * Handles drag events
   */
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  /**
   * Handles drop event
   */
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (!xlsxLoaded || !window.XLSX) {
      setError("Excel processing library is not loaded yet. Please wait a moment.");
      return;
    }

    const files = [...e.dataTransfer.files];
    if (files.length > 0) {
      // Append files if there are already uploaded files
      const shouldAppend = uploadedFiles.length > 0;
      processMultipleFiles(files, shouldAppend);
    }
  };

  /**
   * Handles the file input change event.
   */
  const handleFile = (e) => {
    if (!xlsxLoaded || !window.XLSX) {
      setError(
        "Excel processing library is not loaded yet. Please wait a moment."
      );
      return;
    }
    
    const files = [...e.target.files];
    if (files.length > 0) {
      // Append files if there are already uploaded files
      const shouldAppend = uploadedFiles.length > 0;
      processMultipleFiles(files, shouldAppend);
    }
    e.target.value = "";
  };

  /**
   * Removes a file from the uploaded files list and updates the data
   */
  const removeFile = (fileIndex) => {
    const fileToRemove = uploadedFiles[fileIndex];
    const newUploadedFiles = uploadedFiles.filter((_, index) => index !== fileIndex);
    setUploadedFiles(newUploadedFiles);
    
    if (newUploadedFiles.length === 0) {
      // Clear all data if no files remain
      handleClearData();
    } else {
      // Recalculate data without the removed file
      // This is a simple approach - in a more complex scenario, you might want to 
      // store file-specific row ranges to remove only those rows
      const remainingRowCount = newUploadedFiles.reduce((sum, file) => sum + file.rowCount, 0);
      
      // For now, we'll just update the file count and suggest regenerating summary
      setFileName(`${newUploadedFiles.length} ไฟล์`);
      setSummaryData([]);
      setSummaryGenerated(false);
      
      // Note: In a production app, you'd want to track which rows belong to which file
      // and remove only those specific rows instead of requiring a full reprocess
    }
  };

  /**
   * Handles the download of the processed data.
   */
  const handleDownload = () => {
    if (tableRows.length === 0) {
      setError("No processed data available to download.");
      return;
    }

    const dataToExport = [tableHeaders, ...tableRows];
    const newWorksheet = window.XLSX.utils.aoa_to_sheet(dataToExport);
    const newWorkbook = window.XLSX.utils.book_new();
    window.XLSX.utils.book_append_sheet(
      newWorkbook,
      newWorksheet,
      "Processed Data"
    );

    const wbout = window.XLSX.write(newWorkbook, {
      bookType: "xlsx",
      type: "binary",
    });
    const blob = new Blob([s2ab(wbout)], { type: "application/octet-stream" });

    const originalName =
      fileName.split(".").slice(0, -1).join(".") || "processed";
    saveAs(blob, `${originalName}_processed.xlsx`);
  };

  /**
   * Handles the download of the summary report.
   */
  const handleDownloadSummary = () => {
    const summaryDataToExport = summaryGenerated ? summaryData : generateSummaryReport();
    if (summaryDataToExport.length === 0) {
      setError("No summary data available to download.");
      return;
    }

    // Create summary data with headers
    const summaryHeaders = [
      "ช่วงมูลค่า",
      "จำนวนรายการ",
      "มูลค่า",
      "ประเภทการทำธุรกรรม",
    ];
    const summaryRows = summaryDataToExport.map((item) => [
      item.valueRange,
      item.count,
      item.totalAmount,
      item.transactionType,
    ]);

    // Add total row
    const totalCount = summaryDataToExport.reduce((sum, item) => sum + item.count, 0);
    const totalAmount = summaryDataToExport.reduce(
      (sum, item) => sum + item.totalAmount,
      0
    );
    const totalRow = ["รวม", totalCount, totalAmount, ""];

    const dataToExport = [summaryHeaders, ...summaryRows, [], totalRow];
    const newWorksheet = window.XLSX.utils.aoa_to_sheet(dataToExport);
    const newWorkbook = window.XLSX.utils.book_new();
    window.XLSX.utils.book_append_sheet(
      newWorkbook,
      newWorksheet,
      "Summary Report"
    );

    const wbout = window.XLSX.write(newWorkbook, {
      bookType: "xlsx",
      type: "binary",
    });
    const blob = new Blob([s2ab(wbout)], { type: "application/octet-stream" });

    const originalName =
      fileName.split(".").slice(0, -1).join(".") || "summary";
    const filterSuffix =
      selectedTransactionType !== "all" ? `_${selectedTransactionType}` : "";
    saveAs(blob, `${originalName}_summary_report${filterSuffix}.xlsx`);
  };

  /**
   * Handles clearing the processed data.
   */
  const handleClearData = () => {
    setTableHeaders([]);
    setTableRows([]);
    setFileName("");
    setError(null);
    setSelectedTransactionType("all");
    setUploadedFiles([]);
    setSummaryData([]);
    setSummaryGenerated(false);
  };  
  return (
    <div className="bg-slate-100 min-h-screen p-4 sm:p-8">
      <div className="max-w-7xl mx-auto">
        <div className="w-full max-w-lg mx-auto p-8 bg-white rounded-2xl shadow-lg">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-slate-800">
              โปรแกรมสรุปข้อมูลตามประเภทการทำธุรกรรมและช่วงมูลค่า
            </h1>
            <p className="text-slate-500 mt-2">อัปโหลดไฟล์เพื่อแสดงผล</p>
          </div>
          <label
            htmlFor="file-upload"
            className={`block border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200 ${
              processing || !xlsxLoaded
                ? "border-slate-200 cursor-not-allowed"
                : dragActive
                ? "border-indigo-500 bg-indigo-50"
                : "border-slate-300 hover:border-indigo-500 hover:bg-slate-50"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              id="file-upload"
              className="hidden"
              accept=".xlsx, .xls, .csv"
              onChange={handleFile}
              disabled={processing || !xlsxLoaded}
              multiple
            />
            <div className="flex flex-col items-center">
              {processing ? (
                <LoadingSpinner size="w-12 h-12" color="text-indigo-600" />
              ) : (
                <div className="relative">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`w-12 h-12 mb-3 transition-colors ${
                      dragActive ? "text-indigo-500" : "text-slate-400"
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth="1"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                  {dragActive && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 border-2 border-indigo-500 border-dashed rounded-full animate-pulse"></div>
                    </div>
                  )}
                </div>
              )}
              <span
                className={`font-medium mt-3 ${
                  processing
                    ? "text-indigo-600"
                    : dragActive
                    ? "text-indigo-600"
                    : "text-indigo-600"
                }`}
              >
                {processing
                  ? "กำลังประมวลผล..."
                  : !xlsxLoaded
                  ? "กำลังโหลดไลบรารี..."
                  : dragActive
                  ? "วางไฟล์ที่นี่..."
                  : "เลือกไฟล์หรือลากวางที่นี่"}
              </span>
              {processing && (
                <div className="mt-2 w-64 bg-slate-200 rounded-full h-2">
                  <div
                    className="bg-indigo-600 h-2 rounded-full animate-pulse"
                    style={{ width: "70%" }}
                  ></div>
                </div>
              )}
              <p className="text-slate-400 text-sm mt-1">
                {fileName && !processing && uploadedFiles.length === 0
                  ? `ไฟล์ล่าสุด: ${fileName}`
                  : uploadedFiles.length > 0
                  ? `${uploadedFiles.length} ไฟล์ที่อัปโหลด - ลากวางเพื่อเพิ่มไฟล์`
                  : "รองรับไฟล์ XLSX, XLS, CSV (สามารถเลือกหลายไฟล์)"}
              </p>
            </div>
          </label>

          {/* Uploaded Files List */}
          {uploadedFiles.length > 0 && (
            <div className="mt-6">
              <h3 className="text-sm font-medium text-slate-700 mb-3">
                ไฟล์ที่อัปโหลด ({uploadedFiles.length})
              </h3>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-slate-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <svg
                        className="w-5 h-5 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-slate-700">
                          {file.name}
                        </p>
                        <p className="text-xs text-slate-500">
                          {file.rowCount} รายการ
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(index)}
                      className="p-1 hover:bg-slate-200 rounded transition-colors"
                      title="ลบไฟล์"
                    >
                      <svg
                        className="w-4 h-4 text-slate-400 hover:text-red-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {error && (
            <div
              className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"
              role="alert"
            >
              <strong className="font-bold">เกิดข้อผิดพลาด: </strong>
              <span className="block sm:inline">{error}</span>
            </div>
          )}
        </div>


        {/* Summary Report Section */}
        {tableRows.length > 0 && (
          <div className="mt-8 bg-white rounded-2xl shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-bold text-slate-700 mb-2">
                  รายงานสรุป
                </h2>
                <p className="text-slate-500 text-sm">
                  สรุปข้อมูลตามประเภทการทำธุรกรรมและช่วงมูลค่า
                  {summaryGenerated && (
                    <span className="text-green-600 font-medium ml-2">
                      (สร้างรายงานแล้ว)
                    </span>
                  )}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleGenerateSummary}
                  disabled={summaryLoading}
                  className="px-4 py-2 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {summaryLoading && (
                    <LoadingSpinner size="w-4 h-4" color="text-white" />
                  )}
                  {summaryLoading ? "กำลังสร้าง..." : "สร้างรายงานสรุป"}
                </button>
                <button
                  onClick={handleDownloadSummary}
                  disabled={summaryLoading || (!summaryGenerated && summaryData.length === 0)}
                  className="px-4 py-2 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-75 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  ดาวน์โหลดรายงานสรุป
                </button>
              </div>
            </div>

            {/* Transaction Type Filter */}
            <div className="mb-6">
              <div className="flex items-center gap-3">
                <CustomSelect
                  id="transactionTypeFilter"
                  label="กรองตามประเภทการทำธุรกรรม:"
                  options={transactionTypes}
                  value={selectedTransactionType}
                  onChange={handleTransactionTypeChange}
                  disabled={summaryLoading}
                  className="w-full max-w-xs"
                  searchable={false}
                />
              </div>
            </div>

            {/* Summary Table */}
            {summaryGenerated && summaryData.length > 0 && (
              <div className="overflow-x-auto relative">
                {summaryLoading && (
                  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                    <div className="flex flex-col items-center">
                      <LoadingSpinner size="w-8 h-8" color="text-indigo-600" />
                      <span className="mt-2 text-sm text-slate-600">
                        กำลังสร้างรายงาน...
                      </span>
                    </div>
                  </div>
                )}
                <table
                  className={`min-w-full text-sm text-left text-slate-500 ${
                    summaryLoading ? "opacity-50" : ""
                  }`}
                >
                  <thead className="text-xs text-slate-700 uppercase bg-slate-100">
                    <tr>
                      <th scope="col" className="px-6 py-3 whitespace-nowrap">
                        ช่วงมูลค่า
                      </th>
                      <th scope="col" className="px-6 py-3 whitespace-nowrap">
                        จำนวนรายการ
                      </th>
                      <th scope="col" className="px-6 py-3 whitespace-nowrap">
                        มูลค่า
                      </th>
                      <th scope="col" className="px-6 py-3 whitespace-nowrap">
                        ประเภทการทำธุรกรรม
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {summaryData.map((item, index) => (
                      <tr
                        key={index}
                        className="bg-white border-b hover:bg-slate-50"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          {item.valueRange}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap font-medium">
                          {item.count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {item.totalAmount.toLocaleString("th-TH", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {item.transactionType}
                        </td>
                      </tr>
                    ))}
                    {/* Summary Total Row */}
                    <tr className="bg-slate-50 border-t-2 border-slate-300 font-semibold">
                      <td className="px-6 py-4 whitespace-nowrap text-slate-800">
                        รวม
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-slate-800">
                        {summaryData
                          .reduce((sum, item) => sum + item.count, 0)
                          .toLocaleString("th-TH")}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-slate-800">
                        {summaryData
                          .reduce((sum, item) => sum + item.totalAmount, 0)
                          .toLocaleString("th-TH", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-slate-800"></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            )}

            {/* Summary Statistics */}
            {summaryGenerated && summaryData.length > 0 && !summaryLoading && (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-blue-800 mb-1">
                    รายการทั้งหมด
                  </h3>
                  <p className="text-2xl font-bold text-blue-900">
                    {summaryData
                      .reduce((sum, item) => sum + item.count, 0)
                      .toLocaleString("th-TH")}
                  </p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-green-800 mb-1">
                    มูลค่ารวม
                  </h3>
                  <p className="text-2xl font-bold text-green-900">
                    {summaryData
                      .reduce((sum, item) => sum + item.totalAmount, 0)
                      .toLocaleString("th-TH", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                  </p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-purple-800 mb-1">
                    ประเภทธุรกรรม
                  </h3>
                  <p className="text-2xl font-bold text-purple-900">
                    {
                      new Set(
                        summaryData.map((item) => item.transactionType)
                      ).size
                    }
                  </p>
                </div>
              </div>
            )}

            {/* No Summary Generated Message */}
            {!summaryGenerated && !summaryLoading && (
              <div className="text-center py-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-slate-100 rounded-full mb-4">
                  <svg
                    className="w-8 h-8 text-slate-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-slate-700 mb-2">
                  ยังไม่ได้สร้างรายงานสรุป
                </h3>
                <p className="text-slate-500 mb-4">
                  กดปุ่ม "สร้างรายงานสรุป" เพื่อดูข้อมูลสรุปตามประเภทการทำธุรกรรมและช่วงมูลค่า
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
